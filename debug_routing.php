<?php
// Debug routing script to understand URL rewriting issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Information for URL Routing</h2>";

echo "<h3>Server Variables:</h3>";
echo "<strong>REQUEST_URI:</strong> " . $_SERVER['REQUEST_URI'] . "<br>";
echo "<strong>SCRIPT_NAME:</strong> " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "<strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "<br>";
echo "<strong>DOCUMENT_ROOT:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>PHP_SELF:</strong> " . $_SERVER['PHP_SELF'] . "<br>";

echo "<h3>GET Parameters:</h3>";
var_dump($_GET);

echo "<h3>Current Working Directory:</h3>";
echo getcwd() . "<br>";

echo "<h3>File Existence Check:</h3>";
$p = isset($_GET['p']) ? $_GET['p'] : 'not-set';
echo "<strong>Parameter 'p':</strong> " . $p . "<br>";

$content_file = "content/" . $p . ".php";
echo "<strong>Looking for file:</strong> " . $content_file . "<br>";
echo "<strong>File exists:</strong> " . (file_exists($content_file) ? 'YES' : 'NO') . "<br>";

if (file_exists($content_file)) {
    echo "<strong>File path:</strong> " . realpath($content_file) . "<br>";
}

echo "<h3>Directory Contents:</h3>";
echo "<strong>Content directory files:</strong><br>";
$files = scandir('content');
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        echo "- " . $file . "<br>";
    }
}

echo "<h3>Base URL Test:</h3>";
include './includes/functions.php';
echo "<strong>Base URL from function:</strong> " . $db->base_url() . "<br>";

echo "<h3>Session Check:</h3>";
if (isset($user) && !empty($user)) {
    echo "<strong>User cookie exists:</strong> YES<br>";
    echo "<strong>Is logged in:</strong> " . (is_logged_in($user) ? 'YES' : 'NO') . "<br>";
} else {
    echo "<strong>User cookie exists:</strong> NO<br>";
}

echo "<h3>URL Routing Test:</h3>";
echo "<strong>Testing view-user route:</strong><br>";
echo "- URL should be: " . $db->base_url() . "view-user<br>";
echo "- This should redirect to: " . $db->base_url() . "login (if not logged in)<br>";

echo "<h3>Direct File Access Test:</h3>";
if (file_exists('content/view-user.php')) {
    echo "<strong>view-user.php exists and is readable</strong><br>";
    echo "- File size: " . filesize('content/view-user.php') . " bytes<br>";
    echo "- File permissions: " . substr(sprintf('%o', fileperms('content/view-user.php')), -4) . "<br>";
} else {
    echo "<strong>ERROR: view-user.php not found!</strong><br>";
}
?>
