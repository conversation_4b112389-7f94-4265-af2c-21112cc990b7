<?php
// Test script to verify AJAX endpoints are working
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>AJAX Endpoints Test</h1>";
echo "<p>Testing if AJAX endpoints are accessible with the correct base URL...</p>";

// Include the functions to get base URL
include './includes/functions.php';

$base_url = $db->base_url();
echo "<p><strong>Base URL:</strong> " . $base_url . "</p>";

// List of AJAX endpoints to test
$endpoints = [
    'normal-serverside',
    'bulk-serverside', 
    'inactive-serverside',
    'trial-serverside',
    'reseller-serverside',
    'json-serverside',
    'application-serverside',
    'dns-serverside',
    'log-activity-serverside',
    'log-deleted-serverside',
    'log-credit-serverside',
    'log-bulk-serverside',
    'server-serverside',
    'notification-serverside'
];

echo "<h2>Endpoint Accessibility Test</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Endpoint</th><th>Full URL</th><th>File Exists</th><th>Status</th></tr>";

foreach ($endpoints as $endpoint) {
    $full_url = $base_url . $endpoint;
    $file_path = "content/serverside/" . $endpoint . ".php";
    $file_exists = file_exists($file_path) ? "✓ Yes" : "✗ No";
    
    // Test if the endpoint is accessible
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'content' => 'draw=1&start=0&length=10',
            'timeout' => 5
        ]
    ]);
    
    $status = "Unknown";
    try {
        $response = @file_get_contents($full_url, false, $context);
        if ($response !== false) {
            $json_data = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $status = "✓ Working (Valid JSON)";
            } else {
                $status = "⚠ Response received but not valid JSON";
            }
        } else {
            $status = "✗ No response";
        }
    } catch (Exception $e) {
        $status = "✗ Error: " . $e->getMessage();
    }
    
    echo "<tr>";
    echo "<td>" . $endpoint . "</td>";
    echo "<td>" . $full_url . "</td>";
    echo "<td>" . $file_exists . "</td>";
    echo "<td>" . $status . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>JavaScript Template Files Fixed</h2>";
echo "<p>The following JavaScript template files have been updated to use the correct base URL:</p>";
echo "<ul>";
echo "<li>templates/js/page/viewuser_js.tpl</li>";
echo "<li>templates/js/page/viewreseller_js.tpl</li>";
echo "<li>templates/js/page/json_js.tpl</li>";
echo "<li>templates/js/page/application_js.tpl</li>";
echo "<li>templates/js/page/dns_js.tpl</li>";
echo "<li>templates/js/page/logactivity_js.tpl</li>";
echo "<li>templates/js/page/logdelete_js.tpl</li>";
echo "<li>templates/js/page/logcredit_js.tpl</li>";
echo "<li>templates/js/page/logbulk_js.tpl</li>";
echo "<li>templates/js/page/server_js.tpl</li>";
echo "<li>templates/js/page/notification2_js.tpl</li>";
echo "</ul>";

echo "<h2>Next Steps</h2>";
echo "<p>1. Clear browser cache and reload the pages</p>";
echo "<p>2. Test the view-user page: <a href='" . $base_url . "view-user' target='_blank'>" . $base_url . "view-user</a></p>";
echo "<p>3. Check browser developer tools for any remaining AJAX errors</p>";
echo "<p>4. Delete this test file when testing is complete</p>";
?>
