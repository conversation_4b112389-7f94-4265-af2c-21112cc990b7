<?php
/* Smarty version 3.1.29, created on 2025-07-13 23:17:27
  from "C:\xampp\htdocs\RajaGenWeb\templates\dashboard.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_68741457c61282_32129442',
  'file_dependency' => 
  array (
    '1c6d02389cfb013f9faaed8b1e0f61782f889641' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\dashboard.tpl',
      1 => 1752434168,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:css/custom_css.tpl' => 1,
    'file:apps/topnav.tpl' => 1,
    'file:apps/sidenav.tpl' => 1,
    'file:js/page/custom_js.tpl' => 1,
    'file:js/page/dashboard_js.tpl' => 1,
    'file:js/page/notification_js.tpl' => 1,
    'file:js/page/search_js.tpl' => 1,
  ),
),false)) {
function content_68741457c61282_32129442 ($_smarty_tpl) {
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
<meta charset="UTF-8">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
<title><?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
 — <?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
</title>
<link rel="shortcut icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">
<link rel="icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap-social/bootstrap-social.css">
<link href="https://cdn.datatables.net/v/bs4/dt-1.13.6/b-2.4.2/r-2.5.0/sr-1.3.0/datatables.min.css" rel="stylesheet">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2/dist/css/select2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/summernote/summernote-bs4.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/style.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/components.css">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:css/custom_css.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</head>
<body>

<div class="center" id="loading">
    <div class='building-blocks'>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>

<div class="main-wrapper">

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/topnav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/sidenav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>



<div class="main-content">
<section class="section">
<div class="section-header">
<h1>Dashboard</h1>
<div class="section-header-breadcrumb">
              <div class="breadcrumb-item">Main</div>
              <div class="breadcrumb-item active">Dashboard</div>
            </div>
</div>
<div class="section-body">
<?php echo $_smarty_tpl->tpl_vars['mainte']->value;?>

<?php if ($_smarty_tpl->tpl_vars['user_id_2']->value == '1' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'superadmin' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'developer') {?>  

<div class="fetch-status alert alert-primary alert-dismissible fade">
    <div class="alert-body">
        <button class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
        <div class="alert-title">STATUS</div>
        <hr>
        <strong>DURATION: <br></strong><u><span class="text-uppercase panel-duration"></span></u><br>
        <strong>DOMAIN CREATED: <br></strong><u><span class="text-uppercase stats-created"></span></u><br>
        <strong>DOMAIN EXPIRED: <br></strong><u><span class="text-uppercase stats-expired"></span></u>
    </div>
</div>

<div class="fetch-update alert alert-primary alert-dismissible fade">
<div class="alert-body">
<button class="close" data-dismiss="alert">
<span>&times;</span>
</button>
<i class="fas fa-star-half-alt"></i> What's new on version <code><span class="panel-version"></span></code> :
<ul class="panel-update-list"></ul>
</div>
</div>
<?php }
if ($_smarty_tpl->tpl_vars['user_level_2']->value == 'reseller') {?>

<div class="hero text-white hero-bg-image hero-bg-parallax" style="background-image: url('dist/img/bg.jpg');">
<div class="hero-inner">
<h2>Welcome, <?php echo $_smarty_tpl->tpl_vars['user_name_2']->value;?>
!</h2>
<p class="lead">Thank you for choosing <?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
 as your vpn service provider. We will keep you up to date.</p>
<div class="mt-4">
<a href="add-user" class="btn btn-outline-white btn-lg btn-icon icon-left"><i class="far fa-user"></i> Create Account</a>
</div>
</div>
</div>
<br>
<?php }?>

</div>

<div class="row">

<?php if ($_smarty_tpl->tpl_vars['user_id_2']->value == '1' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'superadmin' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'developer' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'reseller') {?>
    <div class="col-lg-3 col-md-3 col-sm-3">
        <div class="card card-statistic-1 card-primary">
            <div class="card-icon shadow-primary bg-primary">
                <i class="fas fa-user-tie"></i>
            </div>
            <div class="card-wrap">
                <div class="card-header">
                    <h4 class="text-primary">Reseller</h4>
                </div>
            <div class="card-body stats-reseller"><i class="fas fa-spinner fa-spin"></i></div>
            </div>
        </div>
        
        <div class="card card-statistic-1 card-primary">
            <div class="card-icon shadow-primary bg-primary">
                <i class="fas fa-users"></i>
            </div>
            <div class="card-wrap">
                <div class="card-header">
                    <h4 class="text-primary">Users</h4>
                </div>
                <div class="card-body stats-user"><i class="fas fa-spinner fa-spin"></i></div>
            </div>
        </div>
        
        <div class="card card-statistic-1 card-primary">
            <div class="card-icon shadow-primary bg-primary">
                <i class="fas fa-signal"></i>
            </div>
            <div class="card-wrap">
                <div class="card-header">
                    <h4 class="text-primary">Online</h4>
                </div>
                <div class="card-body stats-online"><i class="fas fa-spinner fa-spin"></i></div>
            </div>
        </div>
        
        <?php if ($_smarty_tpl->tpl_vars['user_id_2']->value == '1' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'superadmin' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'developer') {?>
        <div class="card card-statistic-1 card-primary">
            <div class="card-icon shadow-primary bg-primary">
                <i class="fas fa-server"></i>
            </div>
            <div class="card-wrap">
                <div class="card-header">
                    <h4 class="text-primary">Servers</h4>
                </div>
                <div class="card-body stats-server"><i class="fas fa-spinner fa-spin"></i></div>
            </div>
        </div>
        <?php }?>
        
        <?php if ($_smarty_tpl->tpl_vars['user_level_2']->value == 'reseller') {?>
        <div class="card card-statistic-1 card-primary">
            <div class="card-icon shadow-primary bg-primary">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="card-wrap">
                <div class="card-header">
                    <h4 class="text-primary">Reminder</h4>
                </div>
                <div class="card-body"><h5>Secure your account.</h5></div>
            </div>
        </div>
        <?php }?>
        
        <?php if ($_smarty_tpl->tpl_vars['user_id_2']->value == '1' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'superadmin' || $_smarty_tpl->tpl_vars['user_level_2']->value == 'developer') {?>
        <!--div class="card card-primary">
            <div class="card-header">
                <h2 class="section-title">Domain Status</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="text-primary">Domain</label>
                    <input class="form-control stats-domain" type="text" readonly>
                </div>
                <div class="form-group">
                    <label class="text-primary">Created</label>
                    <input class="form-control stats-created" type="text" readonly>
                </div>
                <div class="form-group">
                    <label class="text-primary">Expired</label>
                    <input class="form-control stats-expired" type="text" readonly>
                </div>
            </div>
        </div-->
        <?php }?>
    </div>
    
    <div class="col-lg-4 col-md-4 col-sm-4">
        <div class="card card-primary">
            <div class="card-header">
            <h2 class="section-title">Recent Activities</h2>
            </div>
            
            <div class="card-body"> 
                <div class="text-center activity_spinner">
                    <i class="fas fa-spinner fa-spin"></i> Fetching Data
                </div>
                <div class="activity_id">
                    <ul class="list-unstyled list-unstyled-border stats-activity">
                    
                    </ul>
                    <div class="text-center pt-1 pb-1">
                        <a href="log-activity" class="btn btn-primary">
                            View All
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-5 col-md-5 col-sm-5">
        <div class="card card-primary">
        <div class="card-header">
        <h2 class="section-title">Profile</h2>
        </div>
        <div class="card-body">
        
        <div class="card profile-widget">
                  <div class="profile-widget-header">
                    <div class="profilepic profile-widget-picture" id="ppcture">
                      <?php echo $_smarty_tpl->tpl_vars['avatar3']->value;?>

                    </div>
                    <div class="profile-widget-items">
                      <div class="profile-widget-item">
                        <div class="profile-widget-item-label text-primary">USERNAME</div>
                        <div class="profile-widget-item-value profile-username"><i class="fas fa-spinner fa-spin"></i></div>
                      </div>
                      <div class="profile-widget-item">
                        <div class="profile-widget-item-label text-primary">UPLINE</div>
                        <div class="profile-widget-item-value profile-upline"><i class="fas fa-spinner fa-spin"></i></div>
                      </div>
                      <div class="profile-widget-item">
                        <div class="profile-widget-item-label text-primary">CREDIT</div>
                        <div class="profile-widget-item-value profile-credits"><i class="fas fa-spinner fa-spin"></i></div>
                      </div>
                    </div>
                  </div>
                </div>
        
        
        </div>
        </div>
        
        <div class="card card-primary">
        <div class="card-header">
        <h2 class="section-title">Statistics</h2>
        </div>
        <div class="card-body">
            <div class="text-center activity_spinner">
                    <i class="fas fa-spinner fa-spin"></i> Fetching Data
                </div>
        <canvas id="userchart"></canvas>
        </div>
        </div>
    </div>

</div>
<?php }?>
</div>
</section>
</div>

<div class="modal fade normal-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md normal-modal-dialog" role="document">
<div class="modal-content normal-modal-content">
<div class="modal-header normal-modal-header">
<h5 class="modal-title normal-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body normal-modal-body">
<div class="modal-error normal-modal-error"></div>
<div class="modal-html normal-modal-html"></div>
</div>
</div>
</div>
</div>

<div class="modal fade search-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md search-modal-dialog" role="document">
<div class="modal-content search-modal-content">
<div class="modal-header search-modal-header">
<h5 class="modal-title search-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body search-modal-body">
<div class="modal-error search-modal-error"></div>
<div class="modal-html search-modal-html"></div>
</div>
</div>
</div>
</div>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/popper.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/tooltip.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/js/bootstrap.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/nicescroll/jquery.nicescroll.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/moment.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/time.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/stisla.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/chart.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="https://cdn.datatables.net/v/bs4/dt-1.13.6/b-2.4.2/r-2.5.0/sl-1.7.0/datatables.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2/dist/js/select2.full.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery-ui/jquery-ui.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/bootstrap/assets/jqueryform/jquery.form.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/summernote/summernote-bs4.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.full.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/clipboard.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/scripts.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/custom-select.js"><?php echo '</script'; ?>
>
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/custom_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/dashboard_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/notification_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/search_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</body>
</html><?php }
}
