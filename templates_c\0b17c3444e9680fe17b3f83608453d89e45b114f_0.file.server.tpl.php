<?php
/* Smarty version 3.1.29, created on 2025-07-13 23:18:11
  from "C:\xampp\htdocs\RajaGenWeb\templates\server.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_68741483ee4934_83599318',
  'file_dependency' => 
  array (
    '0b17c3444e9680fe17b3f83608453d89e45b114f' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\server.tpl',
      1 => 1752434329,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:css/custom_css.tpl' => 1,
    'file:apps/topnav.tpl' => 1,
    'file:apps/sidenav.tpl' => 1,
    'file:js/page/custom_js.tpl' => 1,
    'file:js/page/notification_js.tpl' => 1,
    'file:js/page/server_js.tpl' => 1,
    'file:js/page/search_js.tpl' => 1,
  ),
),false)) {
function content_68741483ee4934_83599318 ($_smarty_tpl) {
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
<meta charset="UTF-8">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
<title><?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
 — <?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
</title>
<link rel="shortcut icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">
<link rel="icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<?php echo '<script'; ?>
 src="https://cdnjs.cloudflare.com/ajax/libs/timeago.js/4.0.2/timeago.min.js" integrity="sha512-SVDh1zH5N9ChofSlNAK43lcNS7lWze6DTVx1JCXH1Tmno+0/1jMpdbR8YDgDUfcUrPp1xyE53G42GFrcM0CMVg==" crossorigin="anonymous" referrerpolicy="no-referrer"><?php echo '</script'; ?>
>

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap-social/bootstrap-social.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/datatables.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/css/select.bootstrap4.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/css/select.bootstrap4.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/summernote/summernote-bs4.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/style.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/components.css">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:css/custom_css.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</head>
<body>

<div class="center" id="loading">
    <div class='building-blocks'>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>

<div class="main-wrapper">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/topnav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/sidenav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


<div class="main-content">
<section class="section">
<div class="section-header">
<h1>Server Management</h1>
<div class="section-header-breadcrumb">
<div class="breadcrumb-item">Panel</div>
<div class="breadcrumb-item active">Servers</div>
</div>
</div>
<div class="section-body">
<div class="row">
<div class="col-md-4">
<div class="card card-primary">
<div class="card-header">
<h2 class="section-title">Add Server</h2>
</div>
<div class="card-body">
<div class="section-error">
<div class="errors"></div>
</div>
<div class="alert alert-warning text-center add-server-alert d-none" role="alert">
<i class="fas fa-exclamation-triangle"></i>
<br>
<strong>SERVER INSTALLER IS UNDER MAINTENANCE<br>please try again later. </strong>
</div>

<div class="alert alert-info add-server-message d-none" role="alert">
<div class="add-server-content"> <i class="fas fa-exclamation-triangle"></i> 
<strong class="servermsg"></strong>
</div>
</div>

<form action="" class="add-server d-none" autocomplete="off">
<input type="hidden" name="submitted" name="submitted" value="add_server">
<input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
<div class="row">
<div class="col-md-6">
<div class="form-group">
<label for="servername">Server Name</label>
<input id="servername" type="text" class="form-control servername" name="servername" tabindex="1" required="">
</div>
<div class="form-group">
<label for="serverip">Server Host/Ip</label>
<input id="serverip" type="text" class="form-control serverip" name="serverip" tabindex="1" required="">
</div>
<div class="form-group">
<label for="serverpass">Server Password</label>
<input id="serverpass" type="text" class="form-control serverpass" name="serverpass" tabindex="1" required="">
</div>
<div class="form-group">
<label for="serveruser">Server User</label>
<input id="serveruser" type="text" class="form-control serveruser" name="serveruser" tabindex="1" value="root" required="">
</div>
<div class="form-group">
<label for="serverobfs">Custom Hysteria AUTH</label>
<input id="serverauthstr" type="text" class="form-control serverauthstr" name="serverauthstr" tabindex="1" value="firenet" required="">
</div>
<div class="form-group">
<label for="hysteriatype">Hysteria Type</label>
<select class="form-control select2" id="hysteriatype" name="hysteriatype" data-minimum-results-for-search="-1">
<option value="1" class="text-primary" selected>DEFAULT</option>
<option value="2" class="text-primary">ZIVPN</option>
</select>
</div>
</div>

<div class="col-md-6">
<div class="form-group">
<label for="servertcp">Custom Tcp Port</label>
<input id="servertcp" type="number" class="form-control servertcp" name="servercustomtcp" tabindex="1" value="1194" required="">
</div>
<div class="form-group">
<label for="serverudp">Custom Udp Port</label>
<input id="serverudp" type="number" class="form-control serverudp" name="servercustomudp" tabindex="1" value="110" required="">
</div>
<div class="form-group">
<label for="serverssl">Custom Ssl Port</label>
<input id="serverssl" type="number" class="form-control serverssl" name="servercustomssl" tabindex="1" value="443" required="">
</div>
<div class="form-group">
<label for="servervless">Custom Vless Port</label>
<input id="servervless" type="number" class="form-control servervless" name="servercustomvless" tabindex="1" value="442" required="">
</div>
<div class="form-group">
<label for="serverport">Server Port</label>
<input id="serverport" type="number" class="form-control serverport" name="serverport" tabindex="1" value="22" required="">
</div>
<div class="form-group">
<label for="serverobfs">Custom Hysteria OBFS</label>
<input id="serverobfs" type="text" class="form-control serverobfs" name="serverobfs" tabindex="1" value="firenet" required="">
</div>
</div>

<div class="col-md-12">
<div class="form-group">
<label for="servertype">Server Type</label>
<select class="form-control select2" id="servertype" name="servertype">
<option value="1" selected="" class="text-primary">OPENVPN (HTTP)</option>
<option value="2" class="text-primary">OPENCONNECT (HTTP)</option>
<!--option value="3" class="text-primary">UBUNTU SSH (HTTP)</option-->
<option value="4" class="text-primary">OPENVPN (WS)</option>
<option value="5" class="text-primary">OPENCONNECT (WS)</option>
<option value="41" class="text-primary">HYSTERIA (UDP)</option>
<option value="51" class="text-primary">SOCKSIP (UDP)</option>
<!--option value="6" class="text-primary">UBUNTU SSH (WS)</option>
<option value="7" class="text-primary">UBUNTU PPTP</option-->

<option value="8" class="text-warning">OPENVPN (HTTP)</option>
<option value="9" class="text-warning">OPENCONNECT (HTTP)</option>
<!--option value="10" class="text-warning">DEBIAN SSH (HTTP)</option-->
<option value="11" class="text-warning">OPENVPN (WS)</option>
<option value="12" class="text-warning">OPENCONNECT (WS)</option>
<option value="42" class="text-warning">HYSTERIA (UDP)</option>
<option value="62" class="text-warning">HYSTERIA (UDP FREE)</option>
<option value="31" class="text-warning">XRAY</option-->
<option value="13" class="text-warning">ALL IN ONE (WS)</option>
<!--option value="13" class="text-warning">DEBIAN SSH (WS)</option>
<option value="14" class="text-warning">DEBIAN PPTP</option-->
<option value="14" class="text-warning">PSIPHON</option>
<option value="52" class="text-primary">SOCKSIP (UDP)</option>

<option value="15" class="text-danger">OPENVPN (HTTP)</option>
<!--option value="16" class="text-danger">CENTOS OPENCONNECT (HTTP)</option>
<option value="17" class="text-danger">CENTOS SSH (HTTP)</option-->
<option value="18" class="text-danger">OPENVPN (WS)</option>
<option value="43" class="text-danger">HYSTERIA (UDP)</option>
<!--option value="19" class="text-danger">CENTOS OPENCONNECT (WS)</option-->
<!--option value="20" class="text-danger">CENTOS SSH (WS)</option>
<option value="21" class="text-danger">CENTOS PPTP</option-->
</select>
</div>
</div>

</div>
<div class="form-group">
<button type="submit" class="btn btn-primary btn-block btn-add-server" tabindex="4">Install</button>
</div>
</form>
</div>
</div>
<div class="card">
<div class="card-body">
<a href="https://drive.google.com/uc?export=download&id=1IQP3ZfUNDrMyCQzW_j6UbvEvyUgYnVE6" target="_blank" class="btn btn-primary btn-block">OVPN Config</a>
<!--a href="https://drive.google.com/uc?export=download&id=1CQY_uuH9lTLNCEhVVnSjZdkMfD_ne8Gh" target="_blank" class="btn btn-primary btn-block">OVPN Config (Debian)</a-->
</div>
</div>
</div>
<div class="col-md-8">
<div class="card card-primary">
<div class="card-header">
<h2 class="section-title">Server List</h2>
</div>
<div class="card-body">
<div class="alert alert-danger" role="alert">
<strong>SERVER STATUS INDICATOR</strong>
<ul>
<li><code>ACTIVE</code> - <small>ALL SERVICE GOOD, TOTAL USER WILL SHOW SOMETIME LET THE SERVER DO HIS WORK AND WAIT.</small></li>
<li><code>INACTIVE</code> - <small>SOME SERVICE IS NOT GOOD, RESTART THE SERVER.</small></li>
<li><code>OFFLINE</code> - <small>VPS PROVIDER SHUTDOWN THE SERVER OR DELETED OR DISABLED IT, GET A NEW SERVER.</small></li>
</ul> 
</div>
<div class="table-responsive">
<table class="table table-listserver" style="width: 100%">
<thead>
<tr>
<th>    
<div class="custom-checkbox custom-control">
<input type="checkbox" class="custom-control-input checkbox-parent" id="checkbox-all">
<label for="checkbox-all" class="custom-control-label">&nbsp;</label>
</div>
</th>
<th>Name</th>
<th>Service</th>
<th>Status</th>
<th>Location</th>
<th>Action</th>
</tr>
</thead>
</table>
</div>
</div>
</div>
</div>
</div>
<a href="#" onclick="return false;" class="faz2 btn-multi-delete d-none" type="button"><i class="fa fa-trash"></i></a>
</div>
</section>
</div>

<div class="modal fade normal-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md normal-modal-dialog" role="document">
<div class="modal-content normal-modal-content">
<div class="modal-header normal-modal-header">
<h5 class="modal-title normal-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body normal-modal-body">
<div class="modal-error normal-modal-error"></div>
<div class="modal-html normal-modal-html"></div>
</div>
</div>
</div>
</div>

<div class="modal fade search-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md search-modal-dialog" role="document">
<div class="modal-content search-modal-content">
<div class="modal-header search-modal-header">
<h5 class="modal-title search-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body search-modal-body">
<div class="modal-error search-modal-error"></div>
<div class="modal-html search-modal-html"></div>
</div>
</div>
</div>
</div>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/popper.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/tooltip.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/js/bootstrap.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/nicescroll/jquery.nicescroll.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/moment.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/time.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/stisla.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/chart.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/datatables.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/js/dataTables.select.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.full.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery-ui/jquery-ui.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/bootstrap/assets/jqueryform/jquery.form.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/summernote/summernote-bs4.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/clipboard.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/scripts.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/custom-select.js"><?php echo '</script'; ?>
>
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/custom_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/notification_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/server_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/search_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</body>
</html><?php }
}
