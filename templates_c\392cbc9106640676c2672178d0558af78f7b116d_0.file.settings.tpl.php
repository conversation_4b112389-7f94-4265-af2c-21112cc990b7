<?php
/* Smarty version 3.1.29, created on 2025-07-13 23:28:47
  from "C:\xampp\htdocs\RajaGenWeb\templates\settings.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_687416ff959272_63589869',
  'file_dependency' => 
  array (
    '392cbc9106640676c2672178d0558af78f7b116d' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\settings.tpl',
      1 => 1752434329,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:css/custom_css.tpl' => 1,
    'file:apps/topnav.tpl' => 1,
    'file:apps/sidenav.tpl' => 1,
    'file:js/page/custom_js.tpl' => 1,
    'file:js/page/notification_js.tpl' => 1,
    'file:js/page/settings_js.tpl' => 1,
    'file:js/page/search_js.tpl' => 1,
  ),
),false)) {
function content_687416ff959272_63589869 ($_smarty_tpl) {
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
<meta charset="UTF-8">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
<title><?php echo $_smarty_tpl->tpl_vars['site_name']->value;?>
 — <?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
</title>
<link rel="shortcut icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">
<link rel="icon" href="<?php echo $_smarty_tpl->tpl_vars['site_logo']->value;?>
" type="image/x-icon">

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap-social/bootstrap-social.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/datatables.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/css/select.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.9/summernote-bs4.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/style.css">
<link rel="stylesheet" href="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/css-<?php echo $_smarty_tpl->tpl_vars['site_theme']->value;?>
/components.css">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:css/custom_css.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</head>

<body> 

<div class="center" id="loading">
    <div class='building-blocks'>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>

<div class="main-wrapper">
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/topnav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:apps/sidenav.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>


<div class="main-content">
    <section class="section">
        <div class="section-header">
            <h1>Settings</h1>
            <div class="section-header-breadcrumb">
<div class="breadcrumb-item">Panel</div>
<div class="breadcrumb-item active">Settings</div>
</div>
        </div>
        <div class="section-error">
            <div class="errors"></div>
        </div>
        <div class="section-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card card-primary">
                    <div class="card-header">
                    <h2 class="section-title">General</h2>
                    </div>
                        <div class="card-body">
                            <form class="websettings" accept-charset="UTF-8" autocomplete="off">
                                <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                                <input type="hidden" name="notetitle" id="notetitle" value="<?php echo $_smarty_tpl->tpl_vars['site_note']->value;?>
">
                                <input type="hidden" name="submitted" id="submitted" value="web_settings">
                                <div class="form-group">
                                    <label for="title">Title</label>
                                    <input id="title" type="text" value="" class="form-control title" name="title" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="description">Description</label>
                                    <input id="description" type="text" value="" class="form-control description" name="description" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="owner">Owner</label>
                                    <input id="owner" type="text" value="" class="form-control owner" name="owner" tabindex="1">
                                </div>
                                <div class="form-group">
                                    <label for="images">Logo (Allowed Extension: gif, jpeg, jpg, png), 2MB</label>
                                    <input type="file" id="images" name="images[]" class="input-file">
                                    <div class="input-group">
                                        <input type="text" class="form-control" disabled placeholder="Upload Logo" tabindex="1">
                                        <div class="input-group-append">
                                            <button class="btn btn-primary upload-field" type="button"><i class="fa fa-search"></i> Browse</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="logoshow">Logo Show</label>
                                    <select class="form-control" name="logoshow" id="logoshow" data-minimum-results-for-search="-1">
                                        <option class="logoshow" value="" disabled selected></option>
                                        <option value="1">On</option>
                                        <option value="0">Off</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="maintenance">Maintenance</label>
                                    <select class="form-control" name="maintenance" id="maintenance" data-minimum-results-for-search="-1">
                                        <option class="maintenance" value="" disabled selected></option>
                                        <option value="1">On</option>
                                        <option value="0">Off</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="theme">Theme</label>
                                    <select id="theme" class="form-control select2" name="theme" data-minimum-results-for-search="-1">
                                        <option class="theme" value="" disabled selected></option>
                                        <option value="default">Default</option>
                                        <option value="cyan">Cyan</option>
                                        <option value="dark">Dark</option>
                                        <option value="green">Green</option>
                                        <option value="orange">Orange</option>
                                        <option value="pink">Pink</option>
                                        <option value="red">Red</option>
                                        <option value="yellow">Yellow</option>
                                        <option value="blue">Blue</option>
                                        <option value="purple">Purple</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="note">Login Note</label>
                                    <textarea id="note" class="summernote" name="note"></textarea>
                                </div>
                                <div class="form-group" id="websettings">
                                    <button type="button" class="btn btn-primary btn-confirm-web" tabindex="4"> Confirm</button>
                                    <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                                    <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                
                <div class="card card-primary">
                <div class="card-header">
                <h2 class="section-title">User</h2>
                </div>
                    <div class="card-body">
                        <form action="" class="usersettings" autocomplete="off">
                            <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                            <input type="hidden" name="submitted" id="submitted" value="user_settings">
                            <div class="form-group">
                                <label for="prefix">Prefix</label>
                                <select class="form-control select2" name="prefix" id="prefix" data-minimum-results-for-search="-1">
                                    <option class="prefix" value="" disabled selected></option>
                                    <option value="1">On</option>
                                    <option value="0">Off</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="uprefix">Username Prefix</label>
                                <input id="uprefix" type="text" value="" class="form-control uprefix" name="uprefix" tabindex="1">
                            </div>
                            <div class="form-group">
                                <label for="trialdur">Trial Duration</label>
                                <select class="form-control select2" id="trialdur" name="trialdur" data-minimum-results-for-search="-1">
                                    <option class="trialdur" value="" disabled selected></option>
                                    <option value="1">1 Hour</option>
                                    <option value="2">2 Hours</option>
                                    <option value="3">24 Hours</option>
                                    <option value="4">30 Minutes</option>
                                    <option value="5">3 Days</option>
                                    <option value="6">5 Days</option>
                                </select>
                            </div>
                            <div class="form-group" id="usersettings">
                                <button type="button" class="btn btn-primary btn-confirm-user" tabindex="4"> Confirm</button>
                                <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                                <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
            <div class="card card-primary">
            <div class="card-header">
            <h2 class="section-title">VPN</h2>
            </div>
                <div class="card-body">
                    <div class="alert alert-primary" role="alert">
                        <h6 class="alert-heading"><i class="fas fa-book"></i> Note </h6>
                        <code>Max Vpn Sessions</code> : Limit max vpn session per account.<br>
                        <code>Reset All Vpn Sessions</code> : Reset all vpn sessions, this won't logout already logged in users.
                        Restart all your vpn servers after doing this. It will reset Online user counter too.<br>
                    </div>
                    <form class="vpnsettings" accept-charset="UTF-8" autocomplete="off">
                        <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                        <input type="hidden" name="submitted" id="submitted" value="vpn_session">
                        <div class="form-group">
                            <label for="sessions">Max Vpn Sessions</label>
                            <input id="sessions" type="number" value="" min="2" class="form-control sessions" name="sessions" tabindex="1">
                        </div>
                        <div class="form-group">
                            <label for="resetvpnsessions">Reset Vpn Sessions</label>
                            <select class="form-control select2" name="resetvpnsessions" data-minimum-results-for-search="-1">
                                <option value="0">No</option>
                                <option value="1">Yes</option>
                            </select>
                        </div>
                        <div class="form-group" id="vpnsettings">
                            <button type="button" class="btn btn-primary btn-confirm-session" tabindex="4"> Confirm</button>
                            <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                            <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card card-primary">
            <div class="card-header">
            <h2 class="section-title">Trial</h2>
            </div>
                <div class="card-body">
                    <div class="alert alert-primary" role="alert">
                        <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Warning </h6>
                        <p>This will <code>delete</code> all the trial accounts created.</p>
                    </div>
                    <form class="trialsettings" accept-charset="UTF-8" autocomplete="off">
                        <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                        <input type="hidden" name="submitted" id="submitted" value="trial_reset">
                        <div class="form-group">
                            <input type="text" class="form-control trialcounter" readonly="">
                        </div>
                        <div class="form-group" id="trialsettings">
                            <button type="button" class="btn btn-primary btn-confirm-trial" tabindex="4"> Confirm</button>
                            <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                            <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card card-primary">
            <div class="card-header">
            <h2 class="section-title">Device Reset</h2>
            </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Warning </h6>
                        <p>This will <code>RESET</code> all devices in all vpn accounts</p>
                    </div>
                    <form class="clrdevice" accept-charset="UTF-8" autocomplete="off">
                        <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                        <input type="hidden" name="submitted" id="submitted" value="reset_device">
                        <div class="form-group" id="deletedevice">
                            <button type="button" class="btn btn-primary btn-confirm-clrdevice" tabindex="4"> Confirm</button>
                            <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                            <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card card-primary">
            <div class="card-header">
            <h2 class="section-title">Expired Delete</h2>
            </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Warning </h6>
                        <p>This will <code>DELETE</code> all expired accounts.</p>
                        <p>This will <code>NOT</code> reflect in deleted logs.</p>
                    </div>
                    <form class="deletexpired" accept-charset="UTF-8" autocomplete="off">
                        <input type="hidden" name="_key" id="_key" value="<?php echo $_smarty_tpl->tpl_vars['firenet_encrypt']->value;?>
">
                        <input type="hidden" name="submitted" id="submitted" value="delete_expired">
                        <div class="form-group" id="delete_xpired">
                            <button type="button" class="btn btn-primary btn-confirm-delxpired" tabindex="4"> Confirm</button>
                            <button type="submit" class="btn btn-success btn-confirm-auth d-none" tabindex="4"> Authorize</button>
                            <button type="button" class="btn btn-confirm-cancel btn-danger d-none" tabindex="4">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
            </div>
            </div>
        </div>
    </section>
</div>

<div class="modal fade normal-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md normal-modal-dialog" role="document">
<div class="modal-content normal-modal-content">
<div class="modal-header normal-modal-header">
<h5 class="modal-title normal-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body normal-modal-body">
<div class="modal-error normal-modal-error"></div>
<div class="modal-html normal-modal-html"></div>
</div>
</div>
</div>
</div>

<div class="modal fade search-modalize" role="dialog" aria-labelledby="smallmodal">
<div class="modal-dialog modal-md search-modal-dialog" role="document">
<div class="modal-content search-modal-content">
<div class="modal-header search-modal-header">
<h5 class="modal-title search-modal-title"></h5>
<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<div class="modal-body search-modal-body">
<div class="modal-error search-modal-error"></div>
<div class="modal-html search-modal-html"></div>
</div>
</div>
</div>
</div>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/popper.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/tooltip.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/bootstrap/js/bootstrap.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/nicescroll/jquery.nicescroll.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/moment.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/sweetalert2/sweetalert2.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/time.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/stisla.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/chart.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/datatables.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/datatables/Select-1.2.4/js/dataTables.select.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/jquery-ui/jquery-ui.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/bootstrap/assets/jqueryform/jquery.form.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.9/summernote-bs4.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/modules/select2.full.min.js"><?php echo '</script'; ?>
>

<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/clipboard.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/scripts.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
dist/js/custom-select.js"><?php echo '</script'; ?>
>
<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/custom_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/notification_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/settings_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php $_smarty_tpl->smarty->ext->_subtemplate->render($_smarty_tpl, "file:js/page/search_js.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</body>
</html><?php }
}
